import { Component, Input, ChangeDetectionStrategy } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-intro-message-shimmer',
  standalone: true,
  imports: [CommonModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="intro-message-shimmer-container" [ngClass]="theme + '-theme'">
      <div class="intro-shimmer-line"></div>
      <div class="intro-shimmer-line"></div>
      <div class="intro-shimmer-line"></div>
    </div>
  `,
  styleUrls: ['./ai-card-shimmer.component.scss']
})
export class IntroMessageShimmerComponent {
  @Input() theme: 'light' | 'dark' = 'light';
}
